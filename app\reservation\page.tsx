"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { AnimatePresence, motion } from "framer-motion";
import { CalendarIcon, Check, ChevronLeft, ChevronRight, Clock, CreditCard, Loader2, Users, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

import { Calendar } from "@/components/ui/calendar";
import { CustomerInfo, ParticipantInfo, ServiceWithPricing, TimeSlotInfo } from "@/lib/types";
import { useSearchParams } from "next/navigation";

import { PricingCalculator } from "@/components/PricingCalculator";
import ServiceOptionsSelector from "@/components/ServiceOptionsSelector";
import { DepositSelection } from "@/components/payment/DepositSelection";
import { PaymentWrapper } from "@/components/payment/PaymentWrapper";
import { PaymentType } from "@/lib/types/payments";
import { formatTimeFromISO, formatTimeRangeFromISO } from "@/lib/time-utils";

// Updated types using proper backend types
interface ParticipantWithTier extends ParticipantInfo {
	tier?: {
		id: string;
		tier_name: string;
		price: number;
		min_age: number;
		max_age: number | null;
	};
	price?: number;
}

interface BookingItem {
	service: ServiceWithPricing;
	date: string;
	timeSlot: TimeSlotInfo;
	participants: ParticipantWithTier[];
	totalPrice: number;
	selectedOptions: string[];
	optionsPrice: number;
}

interface FormData extends CustomerInfo {
	specialRequests: string;
}

// Fetch real availability data from API
const fetchTimeSlots = async (
	serviceId: string,
	date: string,
	participantCount: number = 1
): Promise<TimeSlotInfo[]> => {
	try {
		const response = await fetch(
			`/api/services/${serviceId}/timeslots?date=${date}&participants=${participantCount}`
		);
		if (!response.ok) throw new Error("Failed to fetch time slots");
		const data = await response.json();

		return data.data || [];
	} catch (error) {
		console.error("Error fetching time slots:", error);
		return [];
	}
};

const steps = [
	{ id: 1, title: "Services & Options", description: "Choisissez vos excursions et options" },
	{ id: 2, title: "Date & Heure", description: "Sélectionnez vos créneaux" },
	{ id: 3, title: "Participants", description: "Nombre de personnes" },
	{ id: 4, title: "Informations", description: "Vos coordonnées" },
	{ id: 5, title: "Confirmation", description: "Vérifiez votre réservation" },
	{ id: 6, title: "Paiement", description: "Paiement sécurisé" },
];

export default function ReservationPage() {
	const [currentStep, setCurrentStep] = useState(1);
	const [services, setServices] = useState<ServiceWithPricing[]>([]);
	const [selectedService, setSelectedService] = useState<string | null>(null);
	const [bookingItems, setBookingItems] = useState<BookingItem[]>([]);
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
	const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
	const [timeSlots, setTimeSlots] = useState<TimeSlotInfo[]>([]);
	const [timeSlotsLoading, setTimeSlotsLoading] = useState(false);
	const [totalPrice, setTotalPrice] = useState(0);
	const [participants, setParticipants] = useState<any[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
	const [optionsPrice, setOptionsPrice] = useState(0);
	const [servicesLoading, setServicesLoading] = useState(true);
	const [formData, setFormData] = useState<FormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		specialRequests: "",
	});
	const [formErrors, setFormErrors] = useState<Partial<FormData>>({});
	const [reservationId, setReservationId] = useState<string | null>(null);

	const [selectedPaymentType, setSelectedPaymentType] = useState<PaymentType>("full");
	const [paymentAmount, setPaymentAmount] = useState<number>(0);

	const searchParams = useSearchParams();
	const preSelectedServiceId = searchParams.get("service");
	const preSelectedOptions = searchParams.get("options")?.split(",").filter(Boolean) || [];
	const preSelectedParticipants = searchParams.get("participants");
	const preSelectedTotalPrice = searchParams.get("totalPrice");
	const preSelectedOptionsPrice = searchParams.get("optionsPrice");

	// Fetch services from database
	useEffect(() => {
		const fetchServices = async () => {
			try {
				const response = await fetch("/api/services");
				if (!response.ok) throw new Error("Failed to fetch services");
				const data = await response.json();
				setServices(data.services);

				// Handle entry point detection
				if (preSelectedServiceId) {
					// Coming from service detail page - pre-select service and skip to step 2
					const foundService = data.services.find((s: ServiceWithPricing) => s.id === preSelectedServiceId);
					if (foundService) {
						setSelectedService(preSelectedServiceId);

						// Pre-select options if provided
						if (preSelectedOptions.length > 0) {
							setSelectedOptions(preSelectedOptions);
						}

						// Pre-populate participants if provided
						if (preSelectedParticipants) {
							try {
								const participantData = JSON.parse(preSelectedParticipants);
								setParticipants(participantData);
							} catch (error) {
								console.error("Error parsing participant data:", error);
							}
						}

						// Pre-populate pricing if provided
						if (preSelectedTotalPrice) {
							setTotalPrice(parseFloat(preSelectedTotalPrice));
						}
						if (preSelectedOptionsPrice) {
							setOptionsPrice(parseFloat(preSelectedOptionsPrice));
						}

						// Always go to step 2 (date/time selection) - don't skip steps
						setCurrentStep(2);
					}
				}
			} catch (error) {
				console.error("Error fetching services:", error);
			} finally {
				setServicesLoading(false);
			}
		};

		fetchServices();
	}, [preSelectedServiceId]); // Only depend on service ID to prevent infinite loops

	// Supprimer la fonction generateDates car le composant Calendar gère la plage

	// Load time slots when date changes
	useEffect(() => {
		if (selectedDate && selectedService) {
			const loadTimeSlots = async () => {
				setTimeSlotsLoading(true);
				setSelectedTimeSlot(null); // Reset selected time slot when date changes
				try {
					// Format date properly to avoid timezone issues
					const year = selectedDate.getFullYear();
					const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
					const day = String(selectedDate.getDate()).padStart(2, "0");
					const dateString = `${year}-${month}-${day}`;

					const serviceSlots = await fetchTimeSlots(selectedService, dateString, 1);
					setTimeSlots(serviceSlots);
				} catch (error) {
					console.error("Error loading time slots:", error);
					setTimeSlots([]);
				} finally {
					setTimeSlotsLoading(false);
				}
			};

			loadTimeSlots();
		} else {
			setTimeSlots([]);
			setSelectedTimeSlot(null);
			setTimeSlotsLoading(false);
		}
	}, [selectedDate, selectedService]);

	const toggleService = (serviceId: string) => {
		setSelectedService(selectedService === serviceId ? null : serviceId);
	};

	const addBookingItem = (timeSlotTime: string) => {
		if (!selectedService) return;

		const service = services.find((s) => s.id === selectedService);
		const timeSlot = timeSlots.find((slot) => {
			const slotTime = formatTimeFromISO(slot.start_time);
			return slotTime === timeSlotTime;
		});
		if (!service || !selectedDate || !timeSlot) return;

		// Calculate base price from pricing tiers
		const basePrice = service.pricing_tiers?.[0]?.price || 0;

		// Calculate options price
		const currentOptionsPrice = service.options
			? service.options
					.filter((option) => selectedOptions.includes(option.id))
					.reduce((sum, option) => sum + (option.price || 0), 0)
			: 0;

		// Format date properly to avoid timezone issues
		const year = selectedDate.getFullYear();
		const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
		const day = String(selectedDate.getDate()).padStart(2, "0");
		const date = `${year}-${month}-${day}`;

		const time = formatTimeFromISO(timeSlot.start_time); // Use timezone-safe time formatting
		const syntheticTimeSlotId = `${selectedService}|${date}|${time}`;

		const newItem: BookingItem = {
			service,
			date,
			timeSlot: {
				...timeSlot,
				id: syntheticTimeSlotId,
			},
			participants: participants.length > 0 ? participants : [{ age: 25 }], // Use pre-populated participants or default
			totalPrice: totalPrice > 0 ? totalPrice + optionsPrice : basePrice + currentOptionsPrice,
			selectedOptions: [...selectedOptions],
			optionsPrice: optionsPrice > 0 ? optionsPrice : currentOptionsPrice,
		};

		setSelectedTimeSlot(timeSlotTime);
		setBookingItems([newItem]); // Only one booking item since we only allow one service
	};

	const updateParticipants = (index: number, participants: ParticipantWithTier[]) => {
		setBookingItems((prev) =>
			prev.map((item, i) => {
				if (i === index) {
					// Calculate service price based on actual participant prices
					const servicePrice = item.service.fixed_price
						? item.service.base_price // Fixed price uses base price
						: participants.reduce((total, p) => total + (p.price || 0), 0); // Sum individual participant prices

					// Recalculate options price with new participant count
					const calculatedOptionsPrice =
						item.service.options
							?.filter((option) => item.selectedOptions.includes(option.id))
							.reduce((total, option) => {
								const optionPrice = option.per_participant
									? option.price * participants.length
									: option.price;
								return total + optionPrice;
							}, 0) || 0;

					const totalPrice = servicePrice + calculatedOptionsPrice;

					return { ...item, participants, optionsPrice: calculatedOptionsPrice, totalPrice };
				}
				return item;
			})
		);
	};

	const updateSelectedOptions = (index: number, selectedOptions: string[], _optionsPrice: number) => {
		setBookingItems((prev) =>
			prev.map((item, i) => {
				if (i === index) {
					const basePrice = item.service.pricing_tiers?.[0]?.price || item.service.base_price || 0;
					// Calculate options price with per-participant logic
					const calculatedOptionsPrice =
						item.service.options
							?.filter((option) => selectedOptions.includes(option.id))
							.reduce((total, option) => {
								const optionPrice = option.per_participant
									? option.price * item.participants.length
									: option.price;
								return total + optionPrice;
							}, 0) || 0;
					// Handle fixed pricing vs per-participant pricing
					const totalPrice = item.service.fixed_price
						? basePrice + calculatedOptionsPrice // Fixed price doesn't multiply by participant count
						: basePrice * item.participants.length + calculatedOptionsPrice;
					return { ...item, selectedOptions, optionsPrice: calculatedOptionsPrice, totalPrice };
				}
				return item;
			})
		);
	};

	const getTotalPrice = () => {
		return bookingItems.reduce((total, item) => total + item.totalPrice, 0);
	};

	const validateForm = (): boolean => {
		const errors: Partial<FormData> = {};

		if (!formData.firstName.trim()) errors.firstName = "Prénom requis";
		if (!formData.lastName.trim()) errors.lastName = "Nom requis";
		if (!formData.email.trim()) errors.email = "Email requis";
		else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) errors.email = "Email invalide";
		if (!formData.phone.trim()) errors.phone = "Téléphone requis";

		setFormErrors(errors);
		return Object.keys(errors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) return;

		setIsLoading(true);

		try {
			// Create bookings for each booking item
			const bookingPromises = bookingItems.map(async (item) => {
				const bookingData = {
					serviceId: item.service.id,
					timeSlotId: item.timeSlot.id,
					participants: item.participants, // Just age information
					customerInfo: {
						firstName: formData.firstName,
						lastName: formData.lastName,
						email: formData.email,
						phone: formData.phone,
						emergencyContactName: formData.emergencyContactName || undefined,
						emergencyContactPhone: formData.emergencyContactPhone || undefined,
					},
					specialRequests: formData.specialRequests || undefined,
					selectedOptions: item.selectedOptions || [],
				};

				const response = await fetch("/api/bookings", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(bookingData),
				});

				if (!response.ok) {
					const error = await response.json();
					// Use specific validation errors if available, otherwise use generic error
					const errorMessage =
						error.details && error.details.length > 0
							? error.details.join(", ")
							: error.error || "Failed to create booking";
					throw new Error(errorMessage);
				}

				return await response.json();
			});

			const results = await Promise.all(bookingPromises);

			console.log("=== BOOKING COMPLETION DEBUG ===");
			console.log("Booking results:", results);
			console.log("First result:", results[0]);
			console.log("First booking data:", results[0]?.data);

			// All bookings successful - show payment
			const firstBookingId = results[0]?.data?.reservationId;
			console.log("Extracted reservation ID:", firstBookingId);

			setReservationId(firstBookingId);
			setCurrentStep(6); // Move to payment step
		} catch (error) {
			console.error("Booking error:", error);
			setIsLoading(false);
			alert(
				`Erreur lors de la création de la réservation: ${
					error instanceof Error ? error.message : "Erreur inconnue"
				}`
			);
		} finally {
			setIsLoading(false);
		}
	};

	const handlePaymentSuccess = (paymentIntentId: string) => {
		// Redirect to confirmation page
		window.location.href = `/reservation/confirmation?booking=${reservationId}&payment=${paymentIntentId}`;
	};

	const handlePaymentError = (error: string) => {
		console.error("Payment error:", error);

		// Check if this might be an ad blocker issue
		if (error.includes("Failed to fetch") || error.includes("ERR_BLOCKED_BY_CLIENT")) {
			alert(
				`Erreur de réseau: ${error}\n\nSi vous utilisez un bloqueur de publicités, veuillez le désactiver temporairement. Votre paiement pourrait avoir été traité malgré cette erreur. Vérifiez vos emails ou contactez-nous si nécessaire.`
			);
		} else {
			alert(`Erreur de paiement: ${error}`);
		}
		// Stay on payment step to allow retry
	};

	const handlePaymentTypeSelect = (paymentType: PaymentType, amount: number) => {
		setSelectedPaymentType(paymentType);
		setPaymentAmount(amount);
	};

	const canProceedToNextStep = () => {
		switch (currentStep) {
			case 1:
				return selectedService !== null;
			case 2:
				return selectedDate && selectedTimeSlot; // Need date and time slot selected
			case 3:
				return participants.length > 0 && totalPrice > 0;
			case 4:
				return formData.firstName && formData.lastName && formData.email && formData.phone;
			case 5:
				return true; // Can proceed to payment
			case 6:
				return false; // Payment step - no next step
			default:
				return true;
		}
	};

	const formatDate = (dateString: string) => {
		// Parse the date string manually to avoid timezone issues
		const [year, month, day] = dateString.split("-").map(Number);
		const date = new Date(year, month - 1, day); // month is 0-indexed in Date constructor
		return date.toLocaleDateString("fr-FR", {
			weekday: "long",
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">Réservation en ligne</p>
							</div>
						</Link>

						<Link href="/">
							<Button variant="ghost" size="sm">
								<X className="w-4 h-4 mr-2" />
								Fermer
							</Button>
						</Link>
					</div>
				</div>
			</header>

			{/* Progress Steps */}
			<div className="bg-white border-b">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						{steps.map((step, index) => (
							<div key={step.id} className="flex items-center">
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
										currentStep >= step.id
											? "bg-emerald-500 text-white"
											: "bg-gray-200 text-gray-500"
									}`}
								>
									{currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
								</div>
								<div className="ml-2 hidden sm:block">
									<div
										className={`text-sm font-medium ${
											currentStep >= step.id ? "text-emerald-600" : "text-gray-500"
										}`}
									>
										{step.title}
									</div>
									<div className="text-xs text-gray-400">{step.description}</div>
								</div>
								{index < steps.length - 1 && (
									<ChevronRight className="w-4 h-4 text-gray-300 mx-2 hidden sm:block" />
								)}
							</div>
						))}
					</div>
				</div>
			</div>

			<div className="container mx-auto px-4 py-8">
				<div className="max-w-4xl mx-auto">
					<AnimatePresence mode="wait">
						{/* Step 1: Service Selection */}
						{currentStep === 1 && (
							<motion.div
								key="step1"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez vos excursions</h2>
									<p className="text-gray-600">
										Sélectionnez une ou plusieurs activités pour votre aventure
									</p>
								</div>

								<div className="grid md:grid-cols-2 gap-6 mb-8">
									{servicesLoading ? (
										<div className="col-span-2 flex items-center justify-center py-12">
											<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
											<span className="ml-3 text-emerald-600 font-medium">
												Chargement des services...
											</span>
										</div>
									) : (
										services.map((service) => (
											<motion.div
												key={service.id}
												whileHover={{ y: -5 }}
												whileTap={{ scale: 0.98 }}
											>
												<Card
													className={`cursor-pointer transition-all duration-300 ${
														selectedService === service.id
															? "ring-2 ring-emerald-500 bg-emerald-50"
															: "hover:shadow-lg"
													}`}
													onClick={() => toggleService(service.id)}
												>
													<div className="relative">
														<Image
															src={service.image_url || "/placeholder.svg"}
															alt={service.name}
															width={400}
															height={200}
															className="w-full h-48 object-cover rounded-t-lg"
														/>
														{selectedService === service.id && (
															<div className="absolute top-4 right-4 bg-emerald-500 text-white rounded-full p-2">
																<Check className="w-4 h-4" />
															</div>
														)}
														{preSelectedServiceId &&
															preSelectedServiceId === service.id && (
																<div className="absolute top-4 left-4 bg-orange-500 text-white rounded-full px-3 py-1 text-sm font-semibold">
																	Recommandé
																</div>
															)}
														<div className="absolute bottom-4 left-4">
															<Badge className="bg-white/90 text-emerald-700 font-semibold">
																{service.max_age
																	? `${service.min_age}-${service.max_age} ans`
																	: `À partir de ${service.min_age} ans`}
															</Badge>
														</div>
													</div>

													<CardContent className="p-6">
														<h3 className="text-xl font-bold text-gray-900 mb-2">
															{service.name}
														</h3>
														<p className="text-gray-600 mb-4 text-sm">
															{service.description}
														</p>

														<div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
															<div className="flex items-center">
																<Clock className="w-4 h-4 mr-1" />
																{Math.floor(service.duration_minutes / 60)}h
																{service.duration_minutes % 60 > 0
																	? ` ${service.duration_minutes % 60}min`
																	: ""}
															</div>
															<div className="flex items-center">
																<Users className="w-4 h-4 mr-1" />
																Max {service.max_participants}
															</div>
															<div className="flex items-center">
																{service.base_price ||
																	service.pricing_tiers?.[0]?.price ||
																	0}
																€
															</div>
														</div>
													</CardContent>
												</Card>
											</motion.div>
										))
									)}
								</div>

								{/* Service Options Selection */}
								{selectedService && (
									<div className="mt-8">
										{(() => {
											const service = services.find((s) => s.id === selectedService);
											return service?.options && service.options.length > 0 ? (
												<Card className="p-6">
													<ServiceOptionsSelector
														options={service.options}
														selectedOptions={selectedOptions}
														participantCount={
															bookingItems.length > 0
																? bookingItems[0].participants.length
																: 1
														}
														onSelectionChange={(options: string[]) => {
															setSelectedOptions(options);
															// Update booking items if they exist
															if (bookingItems.length > 0) {
																updateSelectedOptions(0, options, optionsPrice);
															}
														}}
														onPriceChange={(price: number) => {
															setOptionsPrice(price);
															// Update booking items if they exist
															if (bookingItems.length > 0) {
																updateSelectedOptions(0, selectedOptions, price);
															}
														}}
													/>
												</Card>
											) : null;
										})()}
									</div>
								)}
							</motion.div>
						)}

						{/* Step 2: Date & Time Selection */}
						{currentStep === 2 && (
							<motion.div
								key="step2"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Choisissez votre date</h2>
									<p className="text-gray-600">Sélectionnez une date puis vos créneaux horaires</p>
								</div>

								{/* Date Selection */}
								<Card className="mb-8">
									<CardHeader>
										<CardTitle className="flex items-center gap-2">
											<CalendarIcon className="w-5 h-5" />
											Sélectionnez une date
										</CardTitle>
									</CardHeader>
									<CardContent className="flex justify-center">
										<Calendar
											mode="single"
											selected={selectedDate}
											onSelect={setSelectedDate}
											initialFocus
											fromDate={new Date()}
											toDate={new Date(new Date().setFullYear(new Date().getFullYear() + 1))} // Un an à partir d'aujourd'hui
											disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))} // Désactiver les dates passées
											className="rounded-md border"
										/>
									</CardContent>
								</Card>

								{/* Time Slot Selection */}
								{selectedDate && (
									<motion.div
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.3 }}
									>
										<div className="space-y-6">
											{selectedService &&
												(() => {
													const service = services.find((s) => s.id === selectedService);
													const slots = timeSlots || [];

													if (!service) return null;

													return (
														<Card key={selectedService}>
															<CardHeader>
																<CardTitle className="flex items-center gap-2">
																	<Clock className="w-5 h-5" />
																	{service?.name}
																</CardTitle>
																<p className="text-sm text-gray-600">
																	{formatDate(
																		`${selectedDate.getFullYear()}-${String(
																			selectedDate.getMonth() + 1
																		).padStart(2, "0")}-${String(
																			selectedDate.getDate()
																		).padStart(2, "0")}`
																	)}{" "}
																	à Choisissez un créneau
																</p>
															</CardHeader>
															<CardContent>
																{timeSlotsLoading ? (
																	<div className="flex items-center justify-center py-8">
																		<div className="flex items-center gap-3">
																			<Loader2 className="w-5 h-5 animate-spin text-emerald-500" />
																			<span className="text-gray-600">
																				Chargement des créneaux disponibles...
																			</span>
																		</div>
																	</div>
																) : slots.length === 0 ? (
																	<div className="text-center py-8">
																		<p className="text-gray-500">
																			Aucun créneau disponible pour cette date
																		</p>
																	</div>
																) : (
																	<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
																		{slots.map((slot) => {
																			// Use timezone-safe time formatting
																			const slotTime = formatTimeFromISO(
																				slot.start_time
																			);
																			const timeSlotDisplay =
																				formatTimeRangeFromISO(
																					slot.start_time,
																					slot.end_time
																				);
																			const isSelected =
																				selectedTimeSlot === slotTime;

																			return (
																				<Button
																					key={`${slot.start_time}-${slot.available_capacity}`}
																					variant={
																						isSelected
																							? "outline"
																							: "outline"
																					}
																					disabled={!slot.is_available}
																					className={`h-auto p-3 flex flex-col transition-all duration-200 ${
																						!slot.is_available
																							? "opacity-50 cursor-not-allowed"
																							: "hover:bg-emerald-50 hover:border-emerald-300 hover:shadow-sm" +
																							  (isSelected
																									? " bg-emerald-50 border-emerald-300 shadow-sm"
																									: "")
																					} text-black`}
																					onClick={() =>
																						slot.is_available &&
																						addBookingItem(slotTime)
																					}
																				>
																					<div className="text-lg font-bold text-black">
																						{timeSlotDisplay}
																					</div>
																					<div className="text-xs text-gray-500">
																						{slot.is_available
																							? `${slot.available_capacity} places`
																							: "Complet"}
																					</div>
																				</Button>
																			);
																		})}
																	</div>
																)}
															</CardContent>
														</Card>
													);
												})()}
										</div>
									</motion.div>
								)}

								{/* Selected Bookings */}
							</motion.div>
						)}

						{/* Step 3: Participants */}
						{currentStep === 3 && (
							<motion.div
								key="step3"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Nombre de participants</h2>
									<p className="text-gray-600">Sélectionnez le nombre et le type de participants</p>
								</div>

								{bookingItems.length > 0 && (
									<div className="space-y-6">
										{/* Service Info */}
										<Card>
											<CardContent className="p-6">
												<div className="flex items-center gap-4 mb-4">
													<Image
														src={bookingItems[0].service.image_url || "/placeholder.svg"}
														alt={bookingItems[0].service.name}
														width={80}
														height={80}
														className="rounded-lg object-cover"
													/>
													<div className="flex-1">
														<h3 className="text-lg font-semibold">
															{bookingItems[0].service.name}
														</h3>
														<p className="text-sm text-gray-600">
															{formatDate(bookingItems[0].date)} à{" "}
															{new Date(
																bookingItems[0].timeSlot.start_time
															).toLocaleTimeString("fr-FR", {
																hour: "2-digit",
																minute: "2-digit",
															})}
														</p>
													</div>
												</div>
											</CardContent>
										</Card>

										{/* Pricing Calculator */}
										<PricingCalculator
											pricingTiers={(bookingItems[0].service.pricing_tiers || []).map((tier) => ({
												...tier,
												min_age: tier.min_age || 0,
												max_age: tier.max_age || null,
											}))}
											maxParticipants={bookingItems[0].service.max_participants || 10}
											basePrice={bookingItems[0].service.base_price || 0}
											fixedPrice={bookingItems[0].service.fixed_price || false}
											initialParticipants={participants}
											onPriceChange={(price, participantList) => {
												setTotalPrice(price);
												setParticipants(participantList);
												// Update booking item with new pricing info
												updateParticipants(
													0,
													participantList.map((p) => ({
														age: p.age,
														tier: p.tier,
														price: p.price,
													}))
												);
											}}
										/>
									</div>
								)}
							</motion.div>
						)}

						{/* Step 4: Personal Information */}
						{currentStep === 4 && (
							<motion.div
								key="step4"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Vos informations</h2>
									<p className="text-gray-600">Renseignez vos coordonnées pour la réservation</p>
								</div>

								<Card>
									<CardContent className="p-6">
										<div className="grid md:grid-cols-2 gap-6">
											<div>
												<Label htmlFor="firstName">Prénom *</Label>
												<Input
													id="firstName"
													value={formData.firstName}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, firstName: e.target.value }))
													}
													className={formErrors.firstName ? "border-red-500" : ""}
												/>
												{formErrors.firstName && (
													<p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>
												)}
											</div>

											<div>
												<Label htmlFor="lastName">Nom *</Label>
												<Input
													id="lastName"
													value={formData.lastName}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, lastName: e.target.value }))
													}
													className={formErrors.lastName ? "border-red-500" : ""}
												/>
												{formErrors.lastName && (
													<p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>
												)}
											</div>

											<div>
												<Label htmlFor="email">Email *</Label>
												<Input
													id="email"
													type="email"
													value={formData.email}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, email: e.target.value }))
													}
													className={formErrors.email ? "border-red-500" : ""}
												/>
												{formErrors.email && (
													<p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
												)}
											</div>

											<div>
												<Label htmlFor="phone">Téléphone *</Label>
												<Input
													id="phone"
													value={formData.phone}
													onChange={(e) =>
														setFormData((prev) => ({ ...prev, phone: e.target.value }))
													}
													className={formErrors.phone ? "border-red-500" : ""}
													placeholder="+33 6 XX XX XX XX"
												/>
												{formErrors.phone && (
													<p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>
												)}
											</div>

											<div className="md:col-span-2">
												<Label htmlFor="specialRequests">Demandes spéciales (optionnel)</Label>
												<Textarea
													id="specialRequests"
													value={formData.specialRequests}
													onChange={(e) =>
														setFormData((prev) => ({
															...prev,
															specialRequests: e.target.value,
														}))
													}
													placeholder="Allergies, besoins particuliers, etc."
													rows={3}
												/>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						)}

						{/* Step 5: Confirmation */}
						{currentStep === 5 && (
							<motion.div
								key="step5"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">
										Confirmation de réservation
									</h2>
									<p className="text-gray-600">
										Vérifiez vos informations avant de procéder au paiement
									</p>
								</div>

								{/* Order Summary */}
								<Card className="mb-8">
									<CardHeader>
										<CardTitle className="text-xl">Récapitulatif de commande</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-6">
											{bookingItems.map((item, index) => (
												<div key={index} className="space-y-3">
													{/* Service Name */}
													<div>
														<h3 className="text-lg font-semibold text-gray-900">
															{item.service.name}
														</h3>
													</div>

													{/* Date and Time */}
													<div className="text-sm text-gray-600">
														{formatDate(item.date)} à{" "}
														{formatTimeRangeFromISO(
															item.timeSlot.start_time,
															item.timeSlot.end_time
														)}
													</div>

													{/* Selected Options */}
													{item.selectedOptions && item.selectedOptions.length > 0 && (
														<div className="space-y-2">
															<h4 className="text-sm font-medium text-gray-700">
																Options sélectionnées:
															</h4>
															<div className="space-y-1">
																{item.selectedOptions.map((optionId) => {
																	const option = item.service.options?.find(
																		(opt) => opt.id === optionId
																	);
																	return option ? (
																		<div
																			key={optionId}
																			className="flex justify-between text-sm"
																		>
																			<span className="text-gray-600">
																				{option.name}
																				{option.price > 0 &&
																				option.per_participant
																					? ` (${
																							item.participants.length
																					  } × ${option.price.toFixed(2)}€)`
																					: ""}
																			</span>
																			<span className="text-gray-900">
																				{option.price > 0
																					? option.per_participant
																						? `${(
																								option.price *
																								item.participants.length
																						  ).toFixed(2)}€`
																						: `${option.price.toFixed(2)}€`
																					: option.quote_based
																					? "Sur devis"
																					: "Gratuit"}
																			</span>
																		</div>
																	) : null;
																})}
															</div>
														</div>
													)}

													{/* Participants and Price */}
													<div className="space-y-2">
														{/* Participants Breakdown */}
														{item.service.fixed_price ? (
															<div className="flex justify-between">
																<span className="text-sm text-gray-600">
																	Prix fixe pour {item.participants.length}{" "}
																	participant
																	{item.participants.length > 1 ? "s" : ""}
																</span>
																<span className="text-sm text-gray-600">
																	{(item.totalPrice - item.optionsPrice).toFixed(2)}€
																</span>
															</div>
														) : (
															<>
																{/* Group participants by tier and show breakdown */}
																{(() => {
																	const tierGroups = item.participants.reduce(
																		(groups, participant) => {
																			// Use the tier information directly from the participant
																			const tierName =
																				participant.tier?.tier_name ||
																				"Standard";
																			const tierPrice =
																				participant.tier?.price ||
																				participant.price ||
																				item.service.base_price ||
																				0;

																			if (!groups[tierName]) {
																				groups[tierName] = {
																					count: 0,
																					price: tierPrice,
																				};
																			}
																			groups[tierName].count++;
																			return groups;
																		},
																		{} as Record<
																			string,
																			{ count: number; price: number }
																		>
																	);

																	return Object.entries(tierGroups).map(
																		([tierName, { count, price }]) => (
																			<div
																				key={tierName}
																				className="flex justify-between"
																			>
																				<span className="text-sm text-gray-600">
																					{count} × {tierName} (
																					{price.toFixed(2)}€)
																				</span>
																				<span className="text-sm text-gray-600">
																					{(count * price).toFixed(2)}€
																				</span>
																			</div>
																		)
																	);
																})()}
															</>
														)}

														{/* Service Total (removed redundant options line and individual total) */}
													</div>
												</div>
											))}

											<Separator className="my-6" />

											{/* Total */}
											<div className="flex justify-between items-center">
												<span className="text-xl font-semibold text-gray-900">Total</span>
												<span className="text-2xl font-bold text-emerald-600">
													{getTotalPrice()}€
												</span>
											</div>

											<Separator className="my-6" />

											{/* Payment Info */}
											<div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
												<div className="space-y-2 text-sm text-emerald-800">
													<div className="flex items-center gap-2">
														<div className="w-1.5 h-1.5 bg-emerald-600 rounded-full"></div>
														<span>Paiement sécurisé par Stripe</span>
													</div>
													<div className="flex items-center gap-2">
														<div className="w-1.5 h-1.5 bg-emerald-600 rounded-full"></div>
														<span>Confirmation immédiate par email</span>
													</div>
													<div className="flex items-center gap-2">
														<div className="w-1.5 h-1.5 bg-emerald-600 rounded-full"></div>
														<span>Support client 7j/7</span>
													</div>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>

								{/* Payment Selection */}
								<DepositSelection
									totalAmount={getTotalPrice()}
									onPaymentTypeSelect={handlePaymentTypeSelect}
									selectedPaymentType={selectedPaymentType}
								/>
							</motion.div>
						)}

						{/* Step 6: Payment */}
						{currentStep === 6 && reservationId && (
							<motion.div
								key="step6"
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
							>
								<div className="text-center mb-8">
									<h2 className="text-3xl font-bold text-gray-900 mb-4">Paiement sécurisé</h2>
									<p className="text-gray-600">Finalisez votre réservation avec le paiement</p>
								</div>

								{/* Payment Form */}
								<PaymentWrapper
									reservationId={reservationId}
									amount={paymentAmount || getTotalPrice()}
									paymentType={selectedPaymentType as PaymentType}
									onSuccess={handlePaymentSuccess}
									onError={handlePaymentError}
								/>
							</motion.div>
						)}
					</AnimatePresence>

					{/* Navigation Buttons */}
					<div className="flex justify-between items-center mt-8 pt-8 border-t">
						<Button
							variant="outline"
							onClick={() => setCurrentStep((prev) => Math.max(1, prev - 1))}
							disabled={currentStep === 1 || currentStep === 6}
							className="flex items-center gap-2"
						>
							<ChevronLeft className="w-4 h-4" />
							Précédent
						</Button>

						{currentStep < 5 ? (
							<Button
								onClick={() => {
									// If moving from step 2 to 3, ensure booking item is created
									if (currentStep === 2 && selectedTimeSlot && bookingItems.length === 0) {
										addBookingItem(selectedTimeSlot);
									}
									setCurrentStep((prev) => prev + 1);
								}}
								disabled={!canProceedToNextStep()}
								className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
							>
								Suivant
								<ChevronRight className="w-4 h-4" />
							</Button>
						) : currentStep === 5 ? (
							<Button
								onClick={handleSubmit}
								disabled={isLoading || !canProceedToNextStep()}
								className="flex items-center gap-2 bg-emerald-500 hover:bg-emerald-600"
							>
								{isLoading ? (
									<>
										<Loader2 className="w-4 h-4 animate-spin" />
										Traitement...
									</>
								) : (
									<>
										Procéder au paiement
										<CreditCard className="w-4 h-4" />
									</>
								)}
							</Button>
						) : null}
					</div>
				</div>
			</div>
		</div>
	);
}
